<main class="container-fluid mt-5">
  <section class="container-fluid d-flex justify-content-center">
    <app-hero-section-header
      [headerTitle]="'Dream Build Launch!'"
      [headerDescription]="'<PERSON><PERSON><PERSON><PERSON> blends Art Science to bring your ideas to life.'"
      [subHeading]="'What would you like to build today?'"></app-hero-section-header>

    <!-- Experience Cards Grid -->
    <div class="studio-cards-grid mx-auto p-3 gap-4">
      <article class="studio-card card-hover-lift rounded-2xl"
               *ngFor="let card of studioCards; trackBy: trackByCardId"
               (click)="navigateToStudio(card, $event)"
               [class.card-disabled]="card.disabled"
               [style.background-color]="getCardBackground()"
               [style.border]="'0.5px solid ' + (theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)')"
               [attr.aria-disabled]="card.disabled"
               role="button"
               tabindex="0"
               [attr.aria-label]="card.title + ' - ' + card.description">
        <div class="d-flex justify-content-between p-4 h-100">
          <div class="d-flex flex-column justify-content-between pe-4 flex-fill">
            <h2 class="card-title mb-3" [style.color]="getCardTextColor()">{{ card.title }}</h2>
            <p class="card-description mb-4" [style.color]="getCardDescriptionColor()">{{ card.description }}</p>
          </div>
          <div class="d-flex align-items-center justify-content-center flex-fill">
            <img
              [src]="card.image"
              [alt]="card.title + ' illustration'"
              class="card-image"
              loading="lazy"
              decoding="async"
              fetchpriority="high">
          </div>
        </div>
      </article>
    </div>
  </section>

  <!-- Divider Section -->
  <section class="mt-5">
    <div class="d-flex align-items-center justify-content-center">
      <img
        src="/assets/icons/divider-light.svg"
        class="divider-image"
        tabindex="0"
        alt="divider"
        loading="lazy"
        decoding="async" />
    </div>
  </section>

  <!-- Recent Projects Section -->
  <section class="mt-4 py-5">
    <app-recent-creation></app-recent-creation>
  </section>
</main>
