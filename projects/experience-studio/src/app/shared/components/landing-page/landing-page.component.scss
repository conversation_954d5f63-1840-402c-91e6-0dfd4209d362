#main-content-container {
  margin-top: 7%;
  .studio-cards-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    max-width: 1000px;

    .studio-card {
      overflow: hidden;
      cursor: pointer;
      border: 1px solid var(--code-viewer-border) !important;
      background-color: var(--code-viewer-bg) !important;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      height: 280px;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        padding: 2px;
        background: linear-gradient(90deg, #8c65f7 0%, #e84393 100%);
        -webkit-mask:
          linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
        mask:
          linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
        z-index: 1;
      }

      &:hover::before {
        opacity: 1;
      }

      .card-title {
        font-family: Mulish, sans-serif;
        font-size: 28px;
        font-weight: 700;
        line-height: 1.2;
      }

      .card-description {
        font-family: Mulish, sans-serif;
        font-size: 16px;
        font-weight: 500;
        line-height: 1.5;
      }

      .card-image {
        height: auto;
        object-fit: contain;
      }
    }
  }

  // Divider Section
  .divider-image {
    width: 80%;
    max-width: 800px;
    height: auto;
  }

  // Responsive Design
  @media (max-width: 1024px) {
    .studio-cards-grid .studio-card {
      height: 260px;

      .card-title {
        font-size: 24px;
      }
      .card-description {
        font-size: 14px;
      }
    }
  }

  @media (max-width: 767px) {
    .studio-cards-grid {
      grid-template-columns: 1fr;

      .studio-card {
        height: 240px;
        .card-title {
          font-size: 22px;
        }
      }
    }
  }

  @media (max-width: 480px) {
    .studio-cards-grid .studio-card {
      height: 220px;

      .card-title {
        font-size: 20px;
      }
      .card-description {
        font-size: 12px;
      }
    }
  }

  @media (min-width: 1420px) {
    .studio-cards-grid {
      max-width: 1200px;
    }
  }
}
