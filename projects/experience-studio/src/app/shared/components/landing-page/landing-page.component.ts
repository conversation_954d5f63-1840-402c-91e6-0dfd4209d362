import { Component, OnInit, ChangeDetectionStrategy } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { HeroSectionHeaderComponent } from '../hero-section-header/hero-section-header.component';
import { CardDataService } from '../../services/data-services/card-data.service';
import { RecentCreationComponent } from '../recent-creation/recent-creation.component';
import { ThemeService } from '../../services/theme-service/theme.service';
import { ToastService } from '../../services/toast.service';
import { CardSelectionService } from '../../services/card-selection.service';
import { SubscriptionManager } from '../../utils/subscription-management.util';

interface StudioCard {
  id: number;
  title: string;
  description: string;
  image: string;
  path: string;
  type: string;
  disabled?: boolean;
}

@Component({
  selector: 'app-landing-page',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    HeroSectionHeaderComponent,
    RecentCreationComponent,
  ],
  templateUrl: './landing-page.component.html',
  styleUrl: './landing-page.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LandingPageComponent implements OnInit {
  theme: 'light' | 'dark' = 'light';
  private subscriptionManager = new SubscriptionManager();

  studioCards: StudioCard[] = [
    {
      id: 1,
      title: 'Generate UI Design',
      description: 'Create and generate UI designs with AI. Transform your ideas into beautiful interfaces.',
      image: '/assets/cards-images/ui_design.svg',
      path: 'prompt',
      type: 'generate-ui-design',
      disabled: false
    },
    {
      id: 2,
      title: 'Generate Application',
      description: 'Create complete applications with AI. From concept to functional code in minutes.',
      image: '/assets/cards-images/app_generation.svg',
      path: 'prompt',
      type: 'image-to-code'
    }
  ];

  // Theme-based styling methods
  getCardBackground(): string {
    return 'transparent';
  }

  getCardTextColor(): string {
    return this.theme === 'dark' ? '#ffffff' : '#1D1D1D';
  }

  getCardDescriptionColor(): string {
    return this.theme === 'dark' ? '#cccccc' : '#595959';
  }

  // TrackBy function for ngFor performance optimization
  trackByCardId(_: number, card: StudioCard): number {
    return card.id;
  }

  constructor(
    private router: Router,
    private cardDataService: CardDataService,
    private themeService: ThemeService,
    private toastService: ToastService,
    private cardSelectionService: CardSelectionService
  ) {}

  ngOnInit(): void {
    // Initialize theme
    this.initTheme();

    // Reset card selection state when landing page is loaded
    this.cardSelectionService.resetSelectionState();
  }

  private initTheme(): void {
    this.theme = this.themeService.getCurrentTheme();
    this.subscriptionManager.subscribe(
      this.themeService.themeObservable,
      (theme: 'light' | 'dark') => {
        this.theme = theme;
      }
    );
  }

  navigateToStudio(card: StudioCard, event: Event): void {
    event.stopPropagation();

    if (card.disabled) {
      this.toastService.info('This feature is in build mode.');
      return;
    }

    this.cardDataService.setSelectedCardTitle(card.title);
    this.cardSelectionService.setCardSelected(true);

    const routePrefix = card.title === 'Generate UI Design'
      ? '/experience/generate-ui-design'
      : '/experience/generate-application';

    this.toastService.info(`Starting ${card.title.split(' ')[1]} generation`);
    this.router.navigate([`${routePrefix}/prompt`]);
  }
}
