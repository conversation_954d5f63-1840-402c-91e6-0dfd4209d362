<div id="recent-creation-wrapper" class="card align-items-center gap-5">
  <div class="d-flex p-2 toggle-layout">
    <div class="d-flex align-items-center justify-content-around w-100">
      <button
        class="toggle-btn"
        [ngClass]="{ active: currentCategory === 'recent' }"
        (click)="switchCategory('recent')"
        type="button"
        [attr.aria-pressed]="currentCategory === 'recent'"
        aria-label="Show recent projects">
        Recent
      </button>
      <button
        class="toggle-btn"
        [ngClass]="{ active: currentCategory === 'all' }"
        (click)="switchCategory('all')"
        type="button"
        [attr.aria-pressed]="currentCategory === 'all'"
        aria-label="Show all projects">
        All
      </button>
    </div>
  </div>

  <div id="cards-container">
    <div
      class="cards-grid row g-4 px-4"
      [ngClass]="{
        'slide-recent': currentCategory === 'recent',
        'slide-all': currentCategory === 'all'
      }">
      <div
        class="col-12 col-sm-6 col-lg-4 col-xl-3"
        *ngFor="let option of getCurrentOptions(); trackBy: trackByFn">
        <awe-cards
          [ngClass]="{ 'skeleton-card': isLoading }"
          (click)="handleSelection(option.id)"
          class="cursor-pointer"
          role="button"
          tabindex="0"
          [attr.aria-label]="option.heading + ' - ' + option.description">
          <div content class="card p-3 gap-2 card-content">
            <ng-container *ngIf="isLoading; else loadedContent">
              <div class="skeleton-title"></div>
              <div class="skeleton-text"></div>
              <div class="skeleton-text"></div>
              <div class="skeleton-text"></div>
            </ng-container>

            <ng-template #loadedContent>
              <awe-heading variant="s1" type="bold">{{ option.heading }}</awe-heading>
              <awe-body-text>{{ option.description }}</awe-body-text>
              <div class="d-flex align-items-center justify-content-between pt-2 action-timestamp-container">
                <div
                  class="cursor-pointer p-2 card-action-btn"
                  [title]="option.actionText || getDefaultActionText(option.type)">
                  {{ option.actionText || getDefaultActionText(option.type) }}
                </div>
                <div class="timestamp" [title]="option.timestamp">{{ option.timestamp }}</div>
              </div>
            </ng-template>
          </div>
        </awe-cards>
      </div>
    </div>
  </div>
</div>
