import { CommonModule } from '@angular/common';
import {
  Component,
  OnInit,
  CUSTOM_ELEMENTS_SCHEMA,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  inject
} from '@angular/core';
import { HeadingComponent, BodyTextComponent, CardsComponent } from '@awe/play-comp-library';
import {
  RecentProjectService,
  Project,
} from '../../services/recent-project-services/recent-project.service';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { CardOption } from '../../models/recent-creation.model';
import { SubscriptionManager, ObserverManager } from '../../utils/subscription-management.util';

@Component({
  selector: 'app-recent-creation',
  imports: [CommonModule, HeadingComponent, BodyTextComponent, CardsComponent],
  standalone: true,
  templateUrl: './recent-creation.component.html',
  styleUrl: './recent-creation.component.scss',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class RecentCreationComponent implements OnInit {
  private readonly recentProjectService = inject(RecentProjectService);
  private readonly router = inject(Router);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly subscriptionManager = new SubscriptionManager();
  private readonly observerManager = new ObserverManager();

  theme: 'light' | 'dark' = 'light';
  selectedId: string | null = null;
  currentCategory: string = 'recent';
  isLoading: boolean = true;
  options: { [category: string]: CardOption[] } = { recent: [], all: [] };

  // Pre-computed action mappings for O(1) lookup performance
  private readonly actionMap = new Map<string, string>([
    ['ui', 'Generate UI'],
    ['app', 'Generate App'],
    ['analysis', 'Design Analysis'],
    ['accessibility', 'Review Accessibility']
  ]);

  readonly trackByFn = (_: number, item: CardOption): string => item.id;

  ngOnInit(): void {
    this.initializePlaceholders();
    this.setupIntersectionObserver();

    if (this.router.url.includes('/experience/main')) {
      this.loadProjects();
    }

    this.subscriptionManager.subscribe(
      this.router.events.pipe(filter(event => event instanceof NavigationEnd)),
      () => {
        if (this.router.url.includes('/experience/main')) {
          this.loadProjects();
        }
      }
    );
  }

  switchCategory(category: string): void {
    if (this.currentCategory !== category) {
      this.currentCategory = category;
      this.animateCategorySwitch(category);
      this.cdr.markForCheck();
    }
  }

  getCurrentOptions(): CardOption[] {
    return this.options[this.currentCategory] || [];
  }

  handleSelection(id: string, event?: Event): void {
    if (event) event.preventDefault();
    this.selectedId = id;
    this.cdr.markForCheck();
  }

  getDefaultActionText(type: string): string {
    return this.actionMap.get(type.toLowerCase()) || 'View';
  }

  private setupIntersectionObserver(): void {
    const element = document.querySelector('.recent-creation-wrapper');
    if (element) {
      const observer = this.observerManager.createIntersectionObserver(
        entries => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              this.loadProjects();
              observer.disconnect();
            }
          });
        },
        { root: null, rootMargin: '100px', threshold: 0.1 }
      );
      observer.observe(element);
    } else {
      this.loadProjects();
    }
  }

  private initializePlaceholders(): void {
    const placeholder = this.createPlaceholder();
    this.options['recent'] = Array(4).fill(null).map(() => ({ ...placeholder }));
    this.options['all'] = Array(12).fill(null).map(() => ({ ...placeholder }));
  }

  private createPlaceholder(): CardOption {
    return { id: '', heading: '', description: '', type: '', timestamp: '' };
  }

  private loadProjects(): void {
    this.isLoading = true;
    this.loadProjectCategory('recent', 4);
    this.loadProjectCategory('all', 12);
  }

  private loadProjectCategory(category: string, limit: number): void {
    this.subscriptionManager.subscribe(
      this.recentProjectService.getUserProjects('<EMAIL>', limit),
      response => this.handleProjectsLoad(response, category),
      () => (this.isLoading = false)
    );
  }

  private handleProjectsLoad(response: any, category: string): void {
    requestAnimationFrame(() => {
      this.options[category] = this.mapProjectsToCardOptions(response.projects);
      if (category === 'recent') {
        this.isLoading = false;
      }
      this.cdr.markForCheck();
    });
  }

  private truncateDescription(description: string): string {
    const words = description.split(' ');
    return words.length > 10 ? words.slice(0, 10).join(' ') + '...' : description;
  }

  private mapProjectsToCardOptions(projects: Project[]): CardOption[] {
    return projects.map(project => ({
      id: project.project_id,
      heading: project.project_name,
      description: this.truncateDescription(project.project_description.replace(/^"|"$/g, '')),
      type: project.project_type.toLowerCase(),
      timestamp: this.recentProjectService.formatDate(project.last_modified),
    }));
  }

  private animateCategorySwitch(category: string): void {
    const gridElement = document.querySelector('.cards-grid') as HTMLElement;
    if (gridElement) {
      gridElement.classList.remove('slide-recent', 'slide-all');
      void gridElement.offsetWidth;
      gridElement.classList.add(category === 'recent' ? 'slide-recent' : 'slide-all');
    }
  }
}
